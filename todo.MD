# 视频场次分割后端 API 文档

## 概述

本文档描述了视频场次分割后端系统的所有API接口，供前端工程师参考使用。

**基础URL**: `http://localhost:8000/api/v1`

**认证方式**: 通过查询参数 `user_id` 进行用户身份验证

## 通用响应格式

### 成功响应
```json
{
  "data": {...},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "detail": "错误信息",
  "status_code": 400
}
```

## 1. 项目管理 API

### 1.1 创建项目
- **URL**: `POST /projects`
- **参数**: 
  - `user_id` (query): 用户ID
  - Body: `ProjectCreate`
- **响应**: `ProjectResponse`

### 1.2 获取项目列表
- **URL**: `GET /projects`
- **参数**: 
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[ProjectResponse]`

### 1.3 获取项目详情
- **URL**: `GET /projects/{project_id}`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
- **响应**: `ProjectResponse`

### 1.4 更新项目
- **URL**: `PUT /projects/{project_id}`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
  - Body: `ProjectUpdate`
- **响应**: `ProjectResponse`

### 1.5 删除项目
- **URL**: `DELETE /projects/{project_id}`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
  - `hard_delete` (query): 是否硬删除，默认false
- **响应**: `{"message": "项目删除成功"}`

### 1.6 获取项目统计
- **URL**: `GET /projects/{project_id}/stats`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
- **响应**: 项目统计信息

## 2. 视频管理 API

### 2.1 扫描项目视频
- **URL**: `POST /projects/{project_id}/scan`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
- **响应**: 扫描结果和统计信息

### 2.2 获取项目视频列表
- **URL**: `GET /projects/{project_id}/videos`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[VideoResponse]`

### 2.3 获取视频详情
- **URL**: `GET /videos/{video_id}`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: `VideoResponse`

### 2.4 删除视频
- **URL**: `DELETE /videos/{video_id}`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: `{"message": "视频删除成功"}`

### 2.5 获取视频处理状态
- **URL**: `GET /videos/{video_id}/processing-status`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: 视频处理状态信息

### 2.6 获取项目视频统计
- **URL**: `GET /projects/{project_id}/videos/stats`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
- **响应**: 视频统计信息

## 3. 视频处理 API

### 3.1 完整处理视频
- **URL**: `POST /videos/{video_id}/process`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: 处理结果（包含语音识别、场次分析、向量化）
- **说明**: 执行完整的视频处理流程

### 3.2 仅处理语音识别
- **URL**: `POST /videos/{video_id}/process/speech`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: 语音识别结果

### 3.3 仅处理场次分析
- **URL**: `POST /videos/{video_id}/process/scenes`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: 场次分析结果

## 4. 场次管理 API

### 4.1 创建场次
- **URL**: `POST /scenes`
- **参数**: 
  - `user_id` (query): 用户ID
  - Body: `SceneCreate`
- **响应**: `SceneResponse`

### 4.2 获取场次详情
- **URL**: `GET /scenes/{scene_id}`
- **参数**: 
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
- **响应**: `SceneResponse`

### 4.3 更新场次
- **URL**: `PUT /scenes/{scene_id}`
- **参数**: 
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
  - Body: `SceneUpdate`
- **响应**: `SceneResponse`

### 4.4 删除场次
- **URL**: `DELETE /scenes/{scene_id}`
- **参数**: 
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
- **响应**: `{"message": "场次删除成功"}`

### 4.5 获取视频场次列表
- **URL**: `GET /videos/{video_id}/scenes`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[SceneResponse]`

### 4.6 获取项目场次列表
- **URL**: `GET /projects/{project_id}/scenes`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[SceneResponse]`

### 4.7 搜索场次
- **URL**: `GET /scenes/search`
- **参数**: 
  - `q` (query): 搜索关键词
  - `project_id` (query): 项目ID（可选）
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认50
- **响应**: `List[SceneResponse]`

### 4.8 根据时间范围获取场次
- **URL**: `GET /videos/{video_id}/scenes/time-range`
- **参数**: 
  - `video_id` (path): 视频ID
  - `start_time` (query): 开始时间（秒）
  - `end_time` (query): 结束时间（秒）
  - `user_id` (query): 用户ID
- **响应**: `List[SceneResponse]`

### 4.9 根据人物获取场次
- **URL**: `GET /scenes/by-characters`
- **参数**: 
  - `characters` (query): 人物名称，多个用逗号分隔
  - `project_id` (query): 项目ID（可选）
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认50
- **响应**: `List[SceneResponse]`

### 4.10 根据标签获取场次
- **URL**: `GET /scenes/by-tags`
- **参数**: 
  - `tags` (query): 标签，多个用逗号分隔
  - `project_id` (query): 项目ID（可选）
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认50
- **响应**: `List[SceneResponse]`

### 4.11 人工验证场次
- **URL**: `POST /scenes/{scene_id}/verify`
- **参数**: 
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
- **响应**: `{"message": "场次验证成功", "scene_id": "..."}`

### 4.12 获取项目场次统计
- **URL**: `GET /projects/{project_id}/scenes/stats`
- **参数**: 
  - `project_id` (path): 项目ID
  - `user_id` (query): 用户ID
- **响应**: 场次统计信息

## 5. 字幕管理 API

### 5.1 创建字幕
- **URL**: `POST /subtitles`
- **参数**: 
  - `user_id` (query): 用户ID
  - Body: `SubtitleCreate`
- **响应**: `SubtitleResponse`

### 5.2 获取字幕详情
- **URL**: `GET /subtitles/{subtitle_id}`
- **参数**: 
  - `subtitle_id` (path): 字幕ID
  - `user_id` (query): 用户ID
- **响应**: `SubtitleResponse`

### 5.3 更新字幕
- **URL**: `PUT /subtitles/{subtitle_id}`
- **参数**: 
  - `subtitle_id` (path): 字幕ID
  - `user_id` (query): 用户ID
  - Body: `SubtitleUpdate`
- **响应**: `SubtitleResponse`

### 5.4 删除字幕
- **URL**: `DELETE /subtitles/{subtitle_id}`
- **参数**: 
  - `subtitle_id` (path): 字幕ID
  - `user_id` (query): 用户ID
- **响应**: `{"message": "字幕删除成功"}`

### 5.5 获取视频字幕列表
- **URL**: `GET /videos/{video_id}/subtitles`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认1000
- **响应**: `List[SubtitleResponse]`

### 5.6 根据时间范围获取字幕
- **URL**: `GET /videos/{video_id}/subtitles/time-range`
- **参数**: 
  - `video_id` (path): 视频ID
  - `start_time` (query): 开始时间（秒）
  - `end_time` (query): 结束时间（秒）
  - `user_id` (query): 用户ID
- **响应**: `List[SubtitleResponse]`

### 5.7 搜索字幕
- **URL**: `GET /subtitles/search`
- **参数**: 
  - `q` (query): 搜索关键词
  - `video_id` (query): 视频ID（可选）
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[SubtitleResponse]`

### 5.8 根据说话人获取字幕
- **URL**: `GET /subtitles/by-speaker`
- **参数**: 
  - `speaker` (query): 说话人
  - `video_id` (query): 视频ID（可选）
  - `user_id` (query): 用户ID
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[SubtitleResponse]`

### 5.9 导出SRT格式字幕
- **URL**: `GET /videos/{video_id}/subtitles/export/srt`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: SRT文件下载

### 5.10 导出VTT格式字幕
- **URL**: `GET /videos/{video_id}/subtitles/export/vtt`
- **参数**: 
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: VTT文件下载

### 5.11 获取视频字幕统计
- **URL**: `GET /videos/{video_id}/subtitles/stats`
- **参数**:
  - `video_id` (path): 视频ID
  - `user_id` (query): 用户ID
- **响应**: 字幕统计信息

## 6. 场次收藏 API

### 6.1 添加收藏
- **URL**: `POST /scenes/{scene_id}/favorite`
- **参数**:
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
  - Body: `SceneFavoriteCreate`
- **响应**: `SceneFavoriteResponse`

### 6.2 移除收藏
- **URL**: `DELETE /scenes/{scene_id}/favorite`
- **参数**:
  - `scene_id` (path): 场次ID
  - `user_id` (query): 用户ID
- **响应**: `{"message": "收藏移除成功"}`

### 6.3 获取收藏详情
- **URL**: `GET /favorites/{favorite_id}`
- **参数**:
  - `favorite_id` (path): 收藏ID
  - `user_id` (query): 用户ID
- **响应**: `SceneFavoriteResponse`

### 6.4 获取用户收藏列表
- **URL**: `GET /favorites`
- **参数**:
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认100
- **响应**: `List[SceneFavoriteResponse]`

### 6.5 更新收藏备注
- **URL**: `PUT /favorites/{favorite_id}`
- **参数**:
  - `favorite_id` (path): 收藏ID
  - `user_id` (query): 用户ID
  - Body: `SceneFavoriteUpdate`
- **响应**: `SceneFavoriteResponse`

### 6.6 搜索收藏
- **URL**: `GET /favorites/search`
- **参数**:
  - `q` (query): 搜索关键词
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
  - `skip` (query): 跳过数量，默认0
  - `limit` (query): 返回数量，默认50
- **响应**: `List[SceneFavoriteResponse]`

### 6.7 获取收藏统计
- **URL**: `GET /favorites/stats`
- **参数**:
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
- **响应**: 收藏统计信息

## 7. 搜索 API

### 7.1 搜索场次（混合搜索）
- **URL**: `GET /search/scenes`
- **参数**:
  - `q` (query): 搜索关键词
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
  - `limit` (query): 返回数量，默认20
- **响应**: 搜索结果（包含向量搜索和文本搜索）

### 7.2 搜索字幕
- **URL**: `GET /search/subtitles`
- **参数**:
  - `q` (query): 搜索关键词
  - `user_id` (query): 用户ID
  - `video_id` (query): 视频ID（可选）
  - `project_id` (query): 项目ID（可选）
  - `limit` (query): 返回数量，默认50
- **响应**: 字幕搜索结果

### 7.3 获取搜索建议
- **URL**: `GET /search/suggestions`
- **参数**:
  - `q` (query): 搜索关键词
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
  - `limit` (query): 返回数量，默认10
- **响应**: 搜索建议列表

### 7.4 高级搜索场次
- **URL**: `POST /search/scenes/advanced`
- **参数**:
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
  - `limit` (query): 返回数量，默认50
  - Body: 高级搜索过滤条件
- **响应**: 高级搜索结果

**高级搜索过滤条件示例**:
```json
{
  "start_time_min": 0.0,
  "start_time_max": 3600.0,
  "duration_min": 10.0,
  "duration_max": 300.0,
  "characters": ["角色1", "角色2"],
  "tags": ["标签1", "标签2"],
  "location": "地点",
  "mood": "情绪",
  "action_type": "动作类型",
  "ai_generated": true,
  "human_verified": false,
  "confidence_min": 0.8,
  "confidence_max": 1.0,
  "sort_by": "start_time",
  "sort_order": "asc"
}
```

### 7.5 获取搜索统计
- **URL**: `GET /search/stats`
- **参数**:
  - `user_id` (query): 用户ID
  - `project_id` (query): 项目ID（可选）
- **响应**: 搜索统计信息

## 8. 数据模型

### 8.1 ProjectCreate
```json
{
  "name": "项目名称",
  "description": "项目描述",
  "video_directory": "/path/to/videos"
}
```

### 8.2 ProjectResponse
```json
{
  "id": "uuid",
  "name": "项目名称",
  "description": "项目描述",
  "video_directory": "/path/to/videos",
  "user_id": "uuid",
  "status": "active",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 8.3 VideoResponse
```json
{
  "id": "uuid",
  "project_id": "uuid",
  "filename": "video.mp4",
  "file_path": "/path/to/video.mp4",
  "file_size": 1024000,
  "duration": 3600.0,
  "format": "mp4",
  "resolution": "1920x1080",
  "frame_rate": 30.0,
  "video_codec": "h264",
  "audio_codec": "aac",
  "video_metadata": {},
  "processing_status": "completed",
  "speech_recognition_status": "completed",
  "scene_analysis_status": "completed",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 8.4 SceneCreate
```json
{
  "video_id": "uuid",
  "title": "场次标题",
  "description": "场次描述",
  "start_time": 0.0,
  "end_time": 60.0,
  "characters": ["角色1", "角色2"],
  "props": ["道具1", "道具2"],
  "scene_assets": ["资产1", "资产2"],
  "location": "地点",
  "tags": ["标签1", "标签2"],
  "mood": "情绪",
  "action_type": "动作类型",
  "dialogue_summary": "对话摘要",
  "visual_description": "视觉描述",
  "ai_generated": false,
  "human_verified": true,
  "confidence_score": 0.95
}
```

### 8.5 SceneResponse
```json
{
  "id": "uuid",
  "video_id": "uuid",
  "title": "场次标题",
  "description": "场次描述",
  "start_time": 0.0,
  "end_time": 60.0,
  "characters": ["角色1", "角色2"],
  "props": ["道具1", "道具2"],
  "scene_assets": ["资产1", "资产2"],
  "location": "地点",
  "tags": ["标签1", "标签2"],
  "mood": "情绪",
  "action_type": "动作类型",
  "dialogue_summary": "对话摘要",
  "visual_description": "视觉描述",
  "ai_generated": false,
  "human_verified": true,
  "confidence_score": 0.95,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 8.6 SubtitleCreate
```json
{
  "video_id": "uuid",
  "start_time": 0.0,
  "end_time": 5.0,
  "text": "字幕文本",
  "speaker": "说话人",
  "confidence": 95.0,
  "language": "zh-CN",
  "source": "volcano_engine"
}
```

### 8.7 SubtitleResponse
```json
{
  "id": "uuid",
  "video_id": "uuid",
  "start_time": 0.0,
  "end_time": 5.0,
  "text": "字幕文本",
  "speaker": "说话人",
  "confidence": 95.0,
  "language": "zh-CN",
  "source": "volcano_engine",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 8.8 SceneFavoriteCreate
```json
{
  "notes": "收藏备注",
  "tags": ["标签1", "标签2"],
  "rating": 5
}
```

### 8.9 SceneFavoriteResponse
```json
{
  "id": "uuid",
  "scene_id": "uuid",
  "user_id": "uuid",
  "notes": "收藏备注",
  "tags": ["标签1", "标签2"],
  "rating": 5,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "scene": "SceneResponse对象"
}
```

## 9. 错误代码

- **400**: 请求参数错误
- **401**: 未授权访问
- **403**: 权限不足
- **404**: 资源不存在
- **409**: 资源冲突
- **422**: 数据验证失败
- **500**: 服务器内部错误

## 10. 使用示例

### 10.1 创建项目并扫描视频
```javascript
// 1. 创建项目
const project = await fetch('/api/v1/projects?user_id=uuid', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '我的项目',
    description: '项目描述',
    video_directory: '/path/to/videos'
  })
});

// 2. 扫描视频
const scanResult = await fetch(`/api/v1/projects/${project.id}/scan?user_id=uuid`, {
  method: 'POST'
});
```

### 10.2 处理视频并搜索场次
```javascript
// 1. 处理视频
const processResult = await fetch(`/api/v1/videos/${video_id}/process?user_id=uuid`, {
  method: 'POST'
});

// 2. 搜索场次
const searchResult = await fetch(`/api/v1/search/scenes?q=关键词&user_id=uuid&limit=20`);
```

### 10.3 收藏场次
```javascript
// 添加收藏
const favorite = await fetch(`/api/v1/scenes/${scene_id}/favorite?user_id=uuid`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    notes: '很棒的场次',
    tags: ['精彩', '重要'],
    rating: 5
  })
});
```

## 11. 注意事项

1. **用户认证**: 所有API都需要提供 `user_id` 参数进行身份验证
2. **权限控制**: 用户只能访问自己创建的项目和相关资源
3. **异步处理**: 视频处理API是异步的，需要通过状态查询API检查处理进度
4. **文件上传**: 视频文件通过文件系统扫描添加，不支持直接上传
5. **分页**: 列表API支持 `skip` 和 `limit` 参数进行分页
6. **搜索**: 支持文本搜索和向量搜索的混合搜索模式
7. **导出**: 字幕支持SRT和VTT格式导出

————

当前项目的每个接口你似乎都忘记了，使用一个 userid，使用固定的userid（3fa85f64-5717-4562-b3fc-2c963f66afa6）。检查每一接口的格式，防止在出现参数不匹配的问题，现在几乎每一个接口都报错参数不匹配